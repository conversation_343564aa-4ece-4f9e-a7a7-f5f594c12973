#!/usr/bin/env python3
"""
Test script for the enhanced roof outline detection functionality.
This script demonstrates the new CV capabilities added to cv_model.py
"""

import cv2
import numpy as np
import os
from cv_model import (
    analyze_roof_comprehensive,
    detect_roof_outline,
    draw_roof_outline,
    create_roof_analysis_overlay
)

def test_roof_outline_detection(image_path: str):
    """
    Test the roof outline detection functionality
    """
    print(f"🏠 Testing roof outline detection on: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return False
    
    try:
        # Test 1: Basic roof outline detection
        print("📍 Step 1: Detecting roof outline...")
        original_image, roof_contours = detect_roof_outline(image_path)
        print(f"✅ Found {len(roof_contours)} roof contours")
        
        # Test 2: Draw yellow outline like in the example
        print("📍 Step 2: Drawing yellow outline...")
        yellow_outline = draw_roof_outline(original_image, roof_contours, 
                                         outline_color=(0, 255, 255), thickness=6)
        
        # Save the yellow outline image
        outline_path = image_path.replace('.jpg', '_test_yellow_outline.jpg')
        cv2.imwrite(outline_path, yellow_outline)
        print(f"✅ Yellow outline saved to: {outline_path}")
        
        # Test 3: Comprehensive analysis
        print("📍 Step 3: Running comprehensive analysis...")
        analysis_result = analyze_roof_comprehensive(image_path)
        
        if 'error' in analysis_result:
            print(f"❌ Analysis error: {analysis_result['error']}")
            return False
        
        print("✅ Comprehensive analysis complete!")
        print(f"📊 Analysis results:")
        for key, value in analysis_result.items():
            if isinstance(value, str) and value.endswith('.jpg'):
                print(f"   - {key}: {value}")
            elif isinstance(value, (int, float)):
                print(f"   - {key}: {value}")
            elif isinstance(value, dict):
                print(f"   - {key}: {len(value)} items")
            elif isinstance(value, list):
                print(f"   - {key}: {len(value)} items")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

def create_demo_comparison(image_path: str):
    """
    Create a side-by-side comparison like in the example image
    """
    print(f"🎨 Creating demo comparison for: {image_path}")
    
    try:
        # Run comprehensive analysis
        analysis_result = analyze_roof_comprehensive(image_path)
        
        if 'error' in analysis_result:
            print(f"❌ Cannot create demo: {analysis_result['error']}")
            return None
        
        # Load the different analysis images
        original = cv2.imread(analysis_result.get('original', image_path))
        
        # Create yellow outline version
        _, roof_contours = detect_roof_outline(image_path)
        yellow_outline = draw_roof_outline(original, roof_contours, 
                                         outline_color=(0, 255, 255), thickness=8)
        
        # Load panel overlay if available
        panel_overlay_path = analysis_result.get('panel_overlay', analysis_result.get('overlay_image_path'))
        if panel_overlay_path and os.path.exists(panel_overlay_path):
            panel_overlay = cv2.imread(panel_overlay_path)
        else:
            # Create a simple panel overlay for demo
            panel_overlay = original.copy()
            # Add some colored rectangles to simulate panels
            h, w = panel_overlay.shape[:2]
            for y in range(int(h*0.2), int(h*0.8), 30):
                for x in range(int(w*0.2), int(w*0.8), 40):
                    cv2.rectangle(panel_overlay, (x, y), (x+35, y+25), (0, 255, 0), -1)
                    cv2.rectangle(panel_overlay, (x, y), (x+35, y+25), (255, 255, 255), 2)
        
        # Resize images to same height
        height = 400  # Fixed height for demo
        yellow_resized = cv2.resize(yellow_outline, (int(yellow_outline.shape[1] * height / yellow_outline.shape[0]), height))
        panel_resized = cv2.resize(panel_overlay, (int(panel_overlay.shape[1] * height / panel_overlay.shape[0]), height))
        
        # Create side-by-side comparison
        demo_image = np.hstack([yellow_resized, panel_resized])
        
        # Add labels like in the example
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.8
        font_thickness = 2
        
        # Left side label
        cv2.putText(demo_image, "High-resolution satellite view", (10, 30), 
                   font, font_scale, (255, 255, 255), font_thickness)
        cv2.putText(demo_image, "Roof outline detection", (10, 60), 
                   font, font_scale, (0, 255, 255), font_thickness)
        
        # Right side label
        right_x = yellow_resized.shape[1] + 10
        cv2.putText(demo_image, "Computer Vision panel detection", (right_x, 30), 
                   font, font_scale, (255, 255, 255), font_thickness)
        cv2.putText(demo_image, "Solar panel analysis", (right_x, 60), 
                   font, font_scale, (0, 255, 0), font_thickness)
        
        # Save demo image
        demo_path = image_path.replace('.jpg', '_demo_comparison.jpg')
        cv2.imwrite(demo_path, demo_image)
        
        print(f"✅ Demo comparison saved to: {demo_path}")
        return demo_path
        
    except Exception as e:
        print(f"❌ Demo creation failed: {str(e)}")
        return None

def main():
    """
    Main test function
    """
    print("🚀 Starting roof outline detection tests...")
    
    # Test with a sample image (you can change this path)
    test_images = [
        'temp/satellite_single_house.jpg',  # From your Streamlit app
        'test_house.jpg',  # If you have a test image
        'sample_roof.jpg'   # Another test image
    ]
    
    success_count = 0
    
    for image_path in test_images:
        if os.path.exists(image_path):
            print(f"\n{'='*50}")
            print(f"Testing with: {image_path}")
            print('='*50)
            
            # Test basic functionality
            if test_roof_outline_detection(image_path):
                success_count += 1
                
                # Create demo comparison
                demo_path = create_demo_comparison(image_path)
                if demo_path:
                    print(f"🎉 Demo created successfully!")
            
            print(f"{'='*50}\n")
        else:
            print(f"⚠️  Skipping {image_path} (file not found)")
    
    print(f"🏁 Tests completed. {success_count}/{len([p for p in test_images if os.path.exists(p)])} successful")
    
    if success_count == 0:
        print("\n💡 To test the roof outline detection:")
        print("1. Run your Streamlit app and analyze a property")
        print("2. This will create temp/satellite_single_house.jpg")
        print("3. Then run this test script again")
        print("4. Or place a test image in the current directory")

if __name__ == "__main__":
    main()
