"""
Enhanced Solar Roof Analyzer with Computer Vision - cv_model_app.py
Professional solar analysis with CV-based roof detection and PDF reports

Features:
- Ultra-high resolution satellite imagery with CV-based roof detection
- Computer vision solar panel layout generation
- Multi-angle street views
- AI-powered analysis with Gemini 1.5 Flash
- Professional PDF report generation
- Advanced roof zone visualization with CV model
- All Google APIs integration (except Solar API - replaced with CV)
"""

import streamlit as st
import requests
import base64
import json
import os
import csv
import pandas as pd
from datetime import datetime
from PIL import Image, ImageDraw
from typing import Dict, Any, List, Tuple
import time
import math

# Computer Vision imports
import cv2
import numpy as np
import matplotlib.pyplot as plt

# Try to import reportlab, if not available, disable PDF generation
try:
    from reportlab.lib.pagesizes import letter
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image as RLImage, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    PDF_AVAILABLE = True
except ImportError as e:
    PDF_AVAILABLE = False
    print(f"ReportLab import error: {e}")  # Log the error for debugging

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# API Keys
GOOGLE_MAPS_API_KEY = os.getenv('GOOGLE_MAPS_API_KEY')
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')

# CV Model Constants
PANEL_WIDTH_M = 1.6
PANEL_HEIGHT_M = 1.0

# Page configuration
st.set_page_config(
    page_title="🌞 Enhanced Solar Analyzer with CV",
    page_icon="🌞",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced Computer Vision Functions for Precise Building Detection
def detect_target_building_outline(image_path: str, center_lat: float, center_lng: float, zoom_level: int = 20) -> Dict:
    """
    Enhanced building detection that focuses on the specific building at the given coordinates.
    Returns building outline and mask for the target building only.
    """
    image = cv2.imread(image_path)
    if image is None:
        return {'success': False, 'error': 'Could not load image'}

    h, w = image.shape[:2]

    # Calculate the center pixel coordinates (where the target building should be)
    center_x, center_y = w // 2, h // 2

    # Define search radius based on zoom level (higher zoom = smaller radius)
    search_radius = max(50, min(150, 300 - (zoom_level * 10)))

    # Create region of interest around the center coordinates
    roi_x1 = max(0, center_x - search_radius)
    roi_y1 = max(0, center_y - search_radius)
    roi_x2 = min(w, center_x + search_radius)
    roi_y2 = min(h, center_y + search_radius)

    # Extract ROI for focused analysis
    roi = image[roi_y1:roi_y2, roi_x1:roi_x2]

    # Enhanced building detection using multiple techniques
    building_mask = _detect_building_in_roi(roi)

    # Create full-size mask
    full_mask = np.zeros((h, w), dtype=np.uint8)
    if building_mask is not None:
        full_mask[roi_y1:roi_y2, roi_x1:roi_x2] = building_mask

    # Find building contours for precise outline
    contours, _ = cv2.findContours(full_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Select the largest contour near the center (most likely the target building)
    target_contour = None
    if contours:
        # Find contour closest to center
        min_distance = float('inf')
        for contour in contours:
            # Calculate contour centroid
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                distance = np.sqrt((cx - center_x)**2 + (cy - center_y)**2)
                if distance < min_distance and cv2.contourArea(contour) > 500:  # Minimum area threshold
                    min_distance = distance
                    target_contour = contour

    return {
        'success': True,
        'building_mask': full_mask,
        'building_contour': target_contour,
        'roi_bounds': (roi_x1, roi_y1, roi_x2, roi_y2),
        'center_coords': (center_x, center_y),
        'search_radius': search_radius
    }

def _detect_building_in_roi(roi_image: np.ndarray) -> np.ndarray:
    """
    Advanced building detection within the region of interest using multiple CV techniques.
    """
    if roi_image is None or roi_image.size == 0:
        return None

    # Convert to different color spaces for better analysis
    gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)
    hsv = cv2.cvtColor(roi_image, cv2.COLOR_BGR2HSV)

    # Method 1: Adaptive thresholding for roof detection
    adaptive_thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

    # Method 2: Edge detection for building boundaries
    edges = cv2.Canny(gray, 50, 150)

    # Method 3: Color-based segmentation for typical roof colors
    # Define roof color ranges (gray, brown, red roofs)
    roof_masks = []

    # Gray roofs (most common)
    gray_lower = np.array([0, 0, 100])
    gray_upper = np.array([180, 30, 200])
    gray_roof = cv2.inRange(hsv, gray_lower, gray_upper)
    roof_masks.append(gray_roof)

    # Brown/tan roofs
    brown_lower = np.array([10, 50, 100])
    brown_upper = np.array([25, 255, 200])
    brown_roof = cv2.inRange(hsv, brown_lower, brown_upper)
    roof_masks.append(brown_roof)

    # Combine all roof detection methods
    combined_mask = np.zeros_like(gray)
    for mask in roof_masks:
        combined_mask = cv2.bitwise_or(combined_mask, mask)

    # Combine with edge detection
    combined_mask = cv2.bitwise_or(combined_mask, edges)

    # Morphological operations to clean up the mask
    kernel = np.ones((3, 3), np.uint8)
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)

    # Fill holes in the detected building
    combined_mask = cv2.dilate(combined_mask, kernel, iterations=2)
    combined_mask = cv2.erode(combined_mask, kernel, iterations=2)

    return combined_mask

def detect_roof_segmentation(image_path: str, center_lat: float = None, center_lng: float = None, zoom_level: int = 20) -> np.ndarray:
    """
    Enhanced roof segmentation that targets the specific building at given coordinates.
    Falls back to original method if coordinates not provided.
    """
    if center_lat is not None and center_lng is not None:
        # Use enhanced building detection
        result = detect_target_building_outline(image_path, center_lat, center_lng, zoom_level)
        if result['success']:
            return result['building_mask']

    # Fallback to original method
    image = cv2.imread(image_path)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    _, mask = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
    return mask

def estimate_roof_pitch_and_azimuth(roof_mask: np.ndarray) -> Dict:
    """
    Dummy estimator for pitch and azimuth using image dimensions.
    Replace with real elevation data via API or angle estimation later.
    """
    return {
        "pitch": 20.0,
        "azimuth": 180.0,
        "flat_projection": roof_mask.copy()
    }

def tile_roof_with_panels(mask: np.ndarray, elevation_info: Dict, building_contour: np.ndarray = None) -> List[Tuple[int, int, int, int]]:
    """
    Enhanced panel tiling that respects the actual building boundaries.
    Uses building contour for precise panel placement within roof area only.
    """
    h, w = mask.shape
    grid = []

    # Adaptive panel size based on building size
    if building_contour is not None:
        # Calculate building area to determine optimal panel size
        building_area = cv2.contourArea(building_contour)
        if building_area > 10000:  # Large building
            step_x, step_y = 30, 20
        elif building_area > 5000:  # Medium building
            step_x, step_y = 25, 15
        else:  # Small building
            step_x, step_y = 20, 12
    else:
        step_x, step_y = 40, 25  # Default size

    # Find the bounding rectangle of the building
    if building_contour is not None:
        x_bound, y_bound, w_bound, h_bound = cv2.boundingRect(building_contour)
        # Add some padding
        padding = 10
        x_start = max(0, x_bound - padding)
        y_start = max(0, y_bound - padding)
        x_end = min(w, x_bound + w_bound + padding)
        y_end = min(h, y_bound + h_bound + padding)
    else:
        # Use entire image if no contour provided
        x_start, y_start = 0, 0
        x_end, y_end = w, h

    # Tile only within the building boundary
    for y in range(y_start, y_end - step_y, step_y):
        for x in range(x_start, x_end - step_x, step_x):
            # Check if panel center is within the building contour
            panel_center_x = x + step_x // 2
            panel_center_y = y + step_y // 2

            if building_contour is not None:
                # Use point-in-polygon test for precise placement
                point_in_building = cv2.pointPolygonTest(building_contour, (panel_center_x, panel_center_y), False)
                if point_in_building < 0:  # Point is outside the building
                    continue

            # Check if the panel area is mostly within the roof mask
            region = mask[y:y+step_y, x:x+step_x]
            if np.mean(region) > 100:  # mostly within roof
                grid.append((x, y, step_x, step_y))

    return grid

def tile_roof_with_smart_placement(mask: np.ndarray, building_contour: np.ndarray, elevation_info: Dict) -> List[Tuple[int, int, int, int]]:
    """
    Advanced panel placement that optimizes for roof shape and orientation.
    """
    if building_contour is None:
        return tile_roof_with_panels(mask, elevation_info)

    h, w = mask.shape
    grid = []

    # Analyze building orientation
    rect = cv2.minAreaRect(building_contour)
    box = cv2.boxPoints(rect)
    box = np.array(box, dtype=np.int32)  # Fixed: np.int0 deprecated in newer NumPy

    # Calculate building dimensions and orientation
    width_building = rect[1][0]
    height_building = rect[1][1]
    angle = rect[2]

    # Determine optimal panel size based on building size
    building_area = cv2.contourArea(building_contour)
    if building_area > 15000:
        panel_w, panel_h = 35, 22
    elif building_area > 8000:
        panel_w, panel_h = 28, 18
    else:
        panel_w, panel_h = 22, 14

    # Get building bounding box
    x_bound, y_bound, w_bound, h_bound = cv2.boundingRect(building_contour)

    # Create a more intelligent grid based on building orientation
    if abs(angle) < 15 or abs(angle) > 75:  # Building is roughly aligned with image axes
        # Use standard grid approach but optimized
        step_x, step_y = panel_w, panel_h

        for y in range(y_bound, y_bound + h_bound - step_y, step_y):
            for x in range(x_bound, x_bound + w_bound - step_x, step_x):
                panel_center = (x + step_x // 2, y + step_y // 2)

                # Check if panel is within building
                if cv2.pointPolygonTest(building_contour, panel_center, False) >= 0:
                    # Additional check for roof mask
                    region = mask[y:y+step_y, x:x+step_x]
                    if np.mean(region) > 120:  # Higher threshold for better accuracy
                        grid.append((x, y, step_x, step_y))

    return grid

def estimate_solar_heatmap(panel_grid: List[Tuple[int, int, int, int]], elevation_info: Dict) -> List[int]:
    """
    Generate dummy heatmap scores per panel.
    """
    return [np.random.randint(60, 100) for _ in panel_grid]

def draw_panel_overlay(image_path: str, panel_grid: List[Tuple[int, int, int, int]], heatmap: List[int]) -> np.ndarray:
    """
    Draws colored panel rectangles on the image.
    Returns annotated image (np.ndarray).
    """
    image = cv2.imread(image_path)
    overlay = image.copy()

    for (x, y, w, h), score in zip(panel_grid, heatmap):
        color = (0, int(255 * (score / 100)), 0)
        cv2.rectangle(overlay, (x, y), (x + w, y + h), color, -1)

    blended = cv2.addWeighted(overlay, 0.4, image, 0.6, 0)
    return blended

def generate_cv_summary(panel_grid: List[Tuple[int, int, int, int]], heatmap: List[int], elevation_info: Dict) -> Dict:
    """Generate summary statistics from CV analysis"""
    panel_area = PANEL_WIDTH_M * PANEL_HEIGHT_M
    total_panels = len(panel_grid)
    usable_area = total_panels * panel_area
    avg_score = np.mean(heatmap) if heatmap else 0
    kwh_est = usable_area * avg_score * 0.015  # Dummy multiplier

    return {
        "panel_count": total_panels,
        "usable_area_m2": round(usable_area, 2),
        "estimated_kwh_per_year": round(kwh_est, 2),
        "average_efficiency_score": round(avg_score, 1)
    }

def generate_roof_panel_layout(image_path: str, center_lat: float = None, center_lng: float = None, zoom_level: int = 20) -> Dict:
    """Enhanced CV function to generate precise roof panel layout for specific building"""

    # Enhanced building detection if coordinates provided
    building_info = None
    if center_lat is not None and center_lng is not None:
        building_info = detect_target_building_outline(image_path, center_lat, center_lng, zoom_level)
        if building_info['success']:
            roof_mask = building_info['building_mask']
            building_contour = building_info['building_contour']
        else:
            # Fallback to original method
            roof_mask = detect_roof_segmentation(image_path)
            building_contour = None
    else:
        # Original method
        roof_mask = detect_roof_segmentation(image_path)
        building_contour = None

    elevation_info = estimate_roof_pitch_and_azimuth(roof_mask)

    # Use enhanced panel tiling with building contour
    if building_contour is not None:
        panel_grid = tile_roof_with_smart_placement(roof_mask, building_contour, elevation_info)
        # Fallback to regular tiling if smart placement returns too few panels
        if len(panel_grid) < 5:
            panel_grid = tile_roof_with_panels(roof_mask, elevation_info, building_contour)
    else:
        panel_grid = tile_roof_with_panels(roof_mask, elevation_info)

    heatmap = estimate_solar_heatmap(panel_grid, elevation_info)

    # Create the enhanced overlay with yellow outline + panels (right side image)
    overlaid_image = draw_enhanced_panel_overlay(image_path, panel_grid, heatmap, building_contour, building_info)

    # Create clean original image (left side image)
    clean_image = create_clean_building_outline(image_path, building_contour, building_info)

    summary = generate_cv_summary(panel_grid, heatmap, elevation_info)

    # Save both images
    overlay_path = image_path.replace('.jpg', '_cv_overlay.jpg')
    clean_path = image_path.replace('.jpg', '_clean_original.jpg')

    cv2.imwrite(overlay_path, overlaid_image)
    cv2.imwrite(clean_path, clean_image)

    return {
        "overlay_image_path": overlay_path,
        "clean_image_path": clean_path,
        "summary": summary,
        "panel_positions": panel_grid,
        "heatmap": heatmap,
        "elevation_info": elevation_info,
        "building_info": building_info,
        "building_contour": building_contour
    }

def draw_enhanced_panel_overlay(image_path: str, panel_grid: List[Tuple[int, int, int, int]],
                               heatmap: List[int], building_contour: np.ndarray = None,
                               building_info: Dict = None) -> np.ndarray:
    """
    Enhanced panel overlay that shows building outline and precise panel placement.
    Creates the right-side image with yellow building outline AND solar panels.
    """
    image = cv2.imread(image_path)
    overlay = image.copy()

    # Draw building outline first (if available) - PROMINENT YELLOW BORDER
    if building_contour is not None:
        # Draw thick yellow building boundary with glow effect (like in your example image)
        cv2.drawContours(overlay, [building_contour], -1, (0, 200, 200), 8)  # Outer glow
        cv2.drawContours(overlay, [building_contour], -1, (0, 255, 255), 4)  # Main yellow border

    # Draw solar panels with enhanced visualization
    for (x, y, w, h), score in zip(panel_grid, heatmap):
        # Color based on efficiency score - using vibrant colors like in the example
        if score > 85:
            color = (0, 255, 0)  # Bright green for high efficiency
        elif score > 70:
            color = (0, 255, 255)  # Bright yellow for good efficiency
        elif score > 55:
            color = (0, 165, 255)  # Orange for medium efficiency
        else:
            color = (0, 100, 255)  # Red for low efficiency

        # Draw filled panel with higher opacity
        cv2.rectangle(overlay, (x, y), (x + w, y + h), color, -1)

        # Draw panel border in white for better definition
        cv2.rectangle(overlay, (x, y), (x + w, y + h), (255, 255, 255), 1)

    # Add center marker if building info available
    if building_info and 'center_coords' in building_info:
        center_x, center_y = building_info['center_coords']
        cv2.circle(overlay, (center_x, center_y), 8, (255, 0, 255), -1)  # Larger magenta center point

    # Blend with original image - higher panel visibility
    blended = cv2.addWeighted(overlay, 0.7, image, 0.3, 0)
    return blended

def create_clean_building_outline(image_path: str, building_contour: np.ndarray = None,
                                building_info: Dict = None) -> np.ndarray:
    """
    Creates a clean satellite image with only the yellow building outline.
    This is for the left-side image in the comparison.
    """
    image = cv2.imread(image_path)

    # For the left side, we want the original clean satellite image
    # No overlays, no panels - just the pure satellite view
    return image

class EnhancedSolarAnalyzerCV:
    """Enhanced solar analysis with Computer Vision and PDF generation"""

    def __init__(self):
        self.google_api_key = GOOGLE_MAPS_API_KEY
        self.gemini_api_key = GEMINI_API_KEY
        self.csv_file = 'searched_addresses.csv'

        if not self.google_api_key:
            st.error("❌ Google Maps API key not found. Please check your .env file.")
            st.info("💡 Create a .env file with: GOOGLE_MAPS_API_KEY=your_key_here")
        if not self.gemini_api_key:
            st.error("❌ Gemini API key not found. Please check your .env file.")
            st.info("💡 Create a .env file with: GEMINI_API_KEY=your_key_here")

        # Initialize CSV file if it doesn't exist
        self._init_csv_file()

    def _init_csv_file(self):
        """Initialize CSV file with headers if it doesn't exist"""
        if not os.path.exists(self.csv_file):
            with open(self.csv_file, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(['Timestamp', 'Address', 'Latitude', 'Longitude', 'Analysis_Status'])

    def save_to_csv(self, address: str, lat: float, lng: float, status: str = 'Completed'):
        """Save searched address to CSV file"""
        try:
            with open(self.csv_file, 'a', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                writer.writerow([timestamp, address, lat, lng, status])
        except Exception as e:
            st.warning(f"Could not save to CSV: {str(e)}")

    def get_csv_download_link(self):
        """Generate download link for CSV file"""
        try:
            if os.path.exists(self.csv_file):
                df = pd.read_csv(self.csv_file)
                csv_string = df.to_csv(index=False)
                b64 = base64.b64encode(csv_string.encode()).decode()
                return f'<a href="data:file/csv;base64,{b64}" download="searched_addresses.csv">📥 Download Address History (CSV)</a>'
            else:
                return "No search history available"
        except Exception as e:
            return f"Error generating download link: {str(e)}"

    def geocode_address(self, address: str) -> Dict[str, Any]:
        """Get precise coordinates for the address - perfectly centered"""
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            'address': address,
            'location_type': 'ROOFTOP',  # Request rooftop precision for building center
            'result_type': 'premise',    # Focus on building/premise, not street
            'key': self.google_api_key
        }

        try:
            response = requests.get(url, params=params)
            data = response.json()

            if data['status'] == 'OK' and data['results']:
                result = data['results'][0]
                location = result['geometry']['location']

                # Use the exact geocoded coordinates for perfect centering
                return {
                    'success': True,
                    'lat': float(f"{location['lat']:.6f}"),  # High precision
                    'lng': float(f"{location['lng']:.6f}"),  # High precision
                    'formatted_address': result['formatted_address']
                }
            else:
                return {'success': False, 'error': f"Geocoding failed: {data['status']}"}

        except Exception as e:
            return {'success': False, 'error': f"Geocoding error: {str(e)}"}

    def get_satellite_image(self, lat: float, lng: float, zoom: float = 20.0) -> Dict[str, Any]:
        """Get ultra high-resolution satellite image focused on the single house"""

        # Validate coordinates
        if not (-90 <= lat <= 90) or not (-180 <= lng <= 180):
            return {'success': False, 'error': f"Invalid coordinates: lat={lat}, lng={lng}"}

        url = "https://maps.googleapis.com/maps/api/staticmap"

        # Google Maps Static API only accepts integer zoom levels, so round to nearest int
        zoom_int = int(round(zoom))

        # Ensure zoom is within valid range (15-25 for satellite)
        zoom_int = max(15, min(22, zoom_int))

        params = {
            'center': f"{lat:.6f},{lng:.6f}",
            'zoom': zoom_int,  # Ultra high zoom for single house focus
            'size': '800x800',  # Larger size for better detail
            'maptype': 'satellite',
            'format': 'jpg',
            'key': self.google_api_key
        }

        try:
            # Debug: Print the request URL for troubleshooting
            print(f"🛰️ Satellite API Request: {url}")
            print(f"📍 Parameters: center={lat},{lng}, zoom={zoom_int}, size=800x800")

            response = requests.get(url, params=params)
            if response.status_code == 200:
                # Check if response is actually an image (not an error page)
                content_type = response.headers.get('content-type', '')
                if 'image' not in content_type:
                    return {'success': False, 'error': f"Invalid response type: {content_type}"}

                # Save image
                image_path = 'temp/satellite_single_house.jpg'
                os.makedirs('temp', exist_ok=True)
                with open(image_path, 'wb') as f:
                    f.write(response.content)

                print(f"✅ Satellite image saved: {image_path}")

                return {
                    'success': True,
                    'image_path': image_path,
                    'image_data': response.content
                }
            else:
                error_msg = f"Failed to get satellite image: {response.status_code}"
                if response.content:
                    error_msg += f" - {response.content.decode('utf-8', errors='ignore')[:200]}"
                return {'success': False, 'error': error_msg}

        except Exception as e:
            return {'success': False, 'error': f"Satellite image error: {str(e)}"}

    def get_street_views_all_angles(self, lat: float, lng: float) -> Dict[str, Any]:
        """Get street view images from all angles (front, back, left, right)"""
        angles = {
            'front': 0,      # North
            'right': 90,     # East
            'back': 180,     # South
            'left': 270      # West
        }

        results = {}

        for direction, heading in angles.items():
            url = "https://maps.googleapis.com/maps/api/streetview"
            params = {
                'location': f"{lat},{lng}",
                'size': '400x400',  # Smaller size for multiple views
                'fov': 60,          # Narrower field of view for house focus
                'heading': heading,
                'pitch': 0,         # Level view
                'key': self.google_api_key
            }

            try:
                response = requests.get(url, params=params)
                if response.status_code == 200:
                    # Save image
                    image_path = f'temp/street_view_{direction}.jpg'
                    os.makedirs('temp', exist_ok=True)
                    with open(image_path, 'wb') as f:
                        f.write(response.content)

                    results[direction] = {
                        'success': True,
                        'image_path': image_path,
                        'heading': heading
                    }
                else:
                    results[direction] = {
                        'success': False,
                        'error': f"Failed to get {direction} view: {response.status_code}"
                    }

            except Exception as e:
                results[direction] = {
                    'success': False,
                    'error': f"{direction} view error: {str(e)}"
                }

        return results

    def get_elevation_data(self, lat: float, lng: float) -> Dict[str, Any]:
        """Get elevation data for the location"""
        url = "https://maps.googleapis.com/maps/api/elevation/json"
        params = {
            'locations': f"{lat},{lng}",
            'key': self.google_api_key
        }

        try:
            response = requests.get(url, params=params)
            data = response.json()

            if data['status'] == 'OK' and data['results']:
                elevation = data['results'][0]['elevation']
                return {
                    'success': True,
                    'elevation': elevation
                }
            else:
                return {'success': False, 'error': f"Elevation API failed: {data['status']}"}

        except Exception as e:
            return {'success': False, 'error': f"Elevation error: {str(e)}"}

    def get_cv_solar_analysis(self, image_path: str, lat: float = None, lng: float = None, zoom_level: int = 20) -> Dict[str, Any]:
        """Enhanced CV-based solar analysis that targets specific building coordinates"""
        try:
            # Use the enhanced CV model with building coordinates
            cv_results = generate_roof_panel_layout(image_path, lat, lng, zoom_level)

            return {
                'success': True,
                'cv_data': cv_results
            }
        except Exception as e:
            return {'success': False, 'error': f"CV analysis error: {str(e)}"}

    def create_cv_solar_overlay(self, image_path: str, cv_data: Dict = None) -> str:
        """Create solar overlay using CV model results"""
        try:
            if cv_data and 'overlay_image_path' in cv_data:
                # CV model already created the overlay
                overlay_path = cv_data['overlay_image_path']

                # Convert CV overlay to PIL format and add professional styling
                cv_image = cv2.imread(overlay_path)
                cv_image_rgb = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(cv_image_rgb)

                # Add legend and professional styling
                enhanced_image = self._add_cv_legend_and_styling(pil_image, cv_data)

                # Save enhanced version
                enhanced_path = 'temp/cv_solar_overlay_enhanced.jpg'
                os.makedirs('temp', exist_ok=True)
                enhanced_image.save(enhanced_path, 'JPEG', quality=95)

                return enhanced_path
            else:
                # Fallback: create basic overlay
                return self._create_basic_cv_overlay(image_path)

        except Exception as e:
            print(f"CV overlay error: {str(e)}")
            return image_path  # Return original if overlay fails

    def _add_cv_legend_and_styling(self, image: Image.Image, cv_data: Dict) -> Image.Image:
        """Add professional legend and styling to CV overlay"""
        draw = ImageDraw.Draw(image)
        width, height = image.size

        # Add legend background
        legend_x, legend_y = 20, height - 150
        legend_bg = [legend_x - 10, legend_y - 10, legend_x + 250, legend_y + 130]
        draw.rectangle(legend_bg, fill=(255, 255, 255, 240), outline=(0, 0, 0, 255), width=2)

        # Legend title
        try:
            draw.text([legend_x, legend_y], "CV Solar Panel Analysis", fill=(0, 0, 0, 255))
        except:
            pass

        # Add summary info from CV data
        if cv_data and 'summary' in cv_data:
            summary = cv_data['summary']
            y_offset = 25

            info_items = [
                f"Panels: {summary.get('panel_count', 0)}",
                f"Area: {summary.get('usable_area_m2', 0)} m²",
                f"Est. kWh/year: {summary.get('estimated_kwh_per_year', 0)}",
                f"Efficiency: {summary.get('average_efficiency_score', 0)}%"
            ]

            for item in info_items:
                try:
                    draw.text([legend_x, legend_y + y_offset], item, fill=(0, 0, 0, 255))
                    y_offset += 20
                except:
                    pass

        return image

    def _create_basic_cv_overlay(self, image_path: str) -> str:
        """Create basic CV overlay when detailed analysis fails"""
        try:
            # Simple fallback overlay
            img = Image.open(image_path)
            draw = ImageDraw.Draw(img, 'RGBA')
            width, height = img.size

            # Add basic roof outline
            margin = 0.15
            roof_left = int(width * margin)
            roof_top = int(height * margin)
            roof_right = int(width * (1 - margin))
            roof_bottom = int(height * (1 - margin))

            # Draw roof boundary
            draw.rectangle([roof_left, roof_top, roof_right, roof_bottom],
                          outline=(255, 255, 0, 255), width=3)

            # Add basic text
            try:
                draw.text([20, 20], "CV Analysis - Basic Mode", fill=(255, 255, 255, 255))
            except:
                pass

            # Save basic overlay
            basic_path = 'temp/cv_basic_overlay.jpg'
            os.makedirs('temp', exist_ok=True)
            img.save(basic_path, 'JPEG', quality=95)

            return basic_path

        except Exception as e:
            print(f"Basic overlay error: {str(e)}")
            return image_path

    def create_professional_highlighted_roof(self, image_path: str, cv_analysis: str = "") -> str:
        """Create a professional highlighted version with clear green/red zones"""
        try:
            # Load the original image
            if cv_analysis:
                print(f"Creating highlighted roof with CV analysis: {len(cv_analysis)} characters")

            img = Image.open(image_path)

            # Create a copy for highlighting
            highlighted_img = img.copy()
            draw = ImageDraw.Draw(highlighted_img, 'RGBA')

            # Get image dimensions
            width, height = img.size

            # Define the house area (center portion of the image)
            house_margin = 0.15  # 15% margin from edges for tighter focus
            house_left = int(width * house_margin)
            house_top = int(height * house_margin)
            house_right = int(width * (1 - house_margin))
            house_bottom = int(height * (1 - house_margin))

            # GREEN ZONES - Optimal solar areas (typically south-facing in northern hemisphere)
            green_zones = [
                # Primary south-facing roof area
                (house_left + int((house_right - house_left) * 0.4),
                 house_top + int((house_bottom - house_top) * 0.1),
                 house_right - int((house_right - house_left) * 0.1),
                 house_top + int((house_bottom - house_top) * 0.6)),

                # Secondary optimal area
                (house_left + int((house_right - house_left) * 0.1),
                 house_top + int((house_bottom - house_top) * 0.3),
                 house_left + int((house_right - house_left) * 0.5),
                 house_top + int((house_bottom - house_top) * 0.7))
            ]

            # Draw green zones with bright, visible highlighting
            for zone in green_zones:
                # Draw filled rectangle with transparency
                draw.rectangle(zone, fill=(0, 255, 0, 120), outline=(0, 200, 0, 255), width=3)

                # Add hatching pattern for better visibility
                x1, y1, x2, y2 = zone
                for i in range(x1, x2, 15):
                    draw.line([(i, y1), (i, y2)], fill=(0, 255, 0, 180), width=2)

            # RED ZONES - Unsuitable areas (north-facing, shaded, obstacles)
            red_zones = [
                # North-facing sections
                (house_left,
                 house_top + int((house_bottom - house_top) * 0.6),
                 house_left + int((house_right - house_left) * 0.4),
                 house_bottom - int((house_bottom - house_top) * 0.1)),

                # Shaded/obstacle areas
                (house_right - int((house_right - house_left) * 0.3),
                 house_top,
                 house_right,
                 house_top + int((house_bottom - house_top) * 0.4))
            ]

            # Draw red zones with bright, visible highlighting
            for zone in red_zones:
                # Draw filled rectangle with transparency
                draw.rectangle(zone, fill=(255, 0, 0, 120), outline=(200, 0, 0, 255), width=3)

                # Add cross-hatch pattern for better visibility
                x1, y1, x2, y2 = zone
                for i in range(x1, x2, 15):
                    draw.line([(i, y1), (i, y2)], fill=(255, 0, 0, 180), width=2)
                for i in range(y1, y2, 15):
                    draw.line([(x1, i), (x2, i)], fill=(255, 0, 0, 180), width=2)

            # Add a bright yellow outline around the main house area
            house_outline = [house_left, house_top, house_right, house_bottom]
            draw.rectangle(house_outline, outline=(255, 255, 0, 255), width=4)

            # Add legend in the corner
            legend_x, legend_y = 20, height - 120
            legend_bg = [legend_x - 10, legend_y - 10, legend_x + 200, legend_y + 100]
            draw.rectangle(legend_bg, fill=(255, 255, 255, 200), outline=(0, 0, 0, 255), width=2)

            # Green legend
            draw.rectangle([legend_x, legend_y, legend_x + 30, legend_y + 20],
                          fill=(0, 255, 0, 180), outline=(0, 200, 0, 255), width=2)

            # Red legend
            draw.rectangle([legend_x, legend_y + 30, legend_x + 30, legend_y + 50],
                          fill=(255, 0, 0, 180), outline=(200, 0, 0, 255), width=2)

            # Yellow outline legend
            draw.rectangle([legend_x, legend_y + 60, legend_x + 30, legend_y + 80],
                          outline=(255, 255, 0, 255), width=3)

            # Save the highlighted image
            highlighted_path = 'temp/satellite_professional_highlighted.jpg'
            highlighted_img.save(highlighted_path, 'JPEG', quality=95)

            return highlighted_path

        except Exception as e:
            return image_path  # Return original if highlighting fails

    def analyze_with_gemini(self, image_path: str, address: str, additional_data: Dict = None) -> Dict[str, Any]:
        """Analyze roof using Gemini 1.5 Flash with CV data"""
        try:
            # Encode image
            with open(image_path, 'rb') as image_file:
                image_content = base64.b64encode(image_file.read()).decode('utf-8')

            # Create comprehensive prompt with CV focus
            prompt = f"""
            🏠 PROFESSIONAL SOLAR ROOF ANALYSIS with COMPUTER VISION for {address}

            Analyze this ultra-high resolution satellite image focusing ONLY on the main house building in the center.
            This analysis uses Computer Vision (CV) technology for roof detection and solar panel placement.

            📊 Technical Data Available:
            {json.dumps(additional_data, indent=2) if additional_data else "No additional data available"}

            Please provide a comprehensive analysis in the following structured format:

            ## 🏠 HOUSE & ROOF IDENTIFICATION
            - Main house location and boundaries
            - Roof type (gabled, hip, flat, complex)
            - Roof material (shingles, tile, metal, etc.)
            - Roof condition assessment
            - Total roof area estimation (sq ft)

            ## 🤖 COMPUTER VISION ANALYSIS
            - CV-detected roof segments and boundaries
            - Automated panel placement optimization
            - Machine learning-based efficiency scoring
            - Pixel-level roof surface analysis

            ## 🟢 OPTIMAL SOLAR ZONES (GREEN AREAS)
            - South-facing roof sections (best orientation)
            - Unobstructed areas with maximum sun exposure
            - CV-identified flat or gently sloped sections
            - Areas free from shadows and obstacles
            - **Percentage of roof suitable for solar: ___%**

            ## 🔴 UNSUITABLE ZONES (RED AREAS)
            - North-facing sections (poor orientation)
            - CV-detected heavily shaded areas
            - Roof obstacles (chimneys, vents, skylights)
            - Steep or complex roof sections identified by CV
            - **Percentage of roof unsuitable: ___%**

            ## 📐 CV-BASED SOLAR PANEL ESTIMATION
            - CV-calculated number of standard panels (300W each)
            - Automated optimal panel layout configuration
            - Expected total system capacity (kW)
            - Annual energy generation estimate (kWh)
            - **System ROI and payback period**

            ## 🌳 ENVIRONMENTAL FACTORS
            - CV-detected tree coverage and seasonal shading impact
            - Neighboring building shadows analysis
            - Roof access for installation and maintenance
            - Local weather considerations

            ## 💡 PROFESSIONAL RECOMMENDATIONS
            - Best installation approach using CV insights
            - Potential challenges and CV-based solutions
            - Maintenance and monitoring suggestions
            - **Cost-benefit analysis with CV optimization**

            ## 📊 TECHNICAL SPECIFICATIONS
            - Recommended inverter type and placement
            - Electrical panel upgrade requirements
            - Structural assessment needs
            - Permit and inspection requirements

            ## 📋 EXECUTIVE SUMMARY
            **Overall Solar Suitability Score: ___/10**
            **Primary Recommendation: [Highly Recommended/Recommended/Consider with Modifications/Not Recommended]**
            **Expected Annual Savings: $____**
            **Installation Investment: $____**
            **Break-even Timeline: ___ years**
            **CV Analysis Confidence: ___% **

            Focus exclusively on the single house at this address. Provide specific, actionable insights with concrete numbers.
            Emphasize the computer vision aspects and automated analysis capabilities.
            """

            payload = {
                "contents": [{
                    "parts": [
                        {"text": prompt},
                        {
                            "inline_data": {
                                "mime_type": "image/jpeg",
                                "data": image_content
                            }
                        }
                    ]
                }],
                "generationConfig": {
                    "temperature": 0.4,
                    "topK": 32,
                    "topP": 1,
                    "maxOutputTokens": 4096,
                }
            }

            headers = {
                'Content-Type': 'application/json',
            }

            # Try API call with simple retry for 503 errors
            max_retries = 2
            for attempt in range(max_retries + 1):
                response = requests.post(
                    f'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={self.gemini_api_key}',
                    headers=headers,
                    data=json.dumps(payload)
                )

                # If successful or not a 503 error, break out of retry loop
                if response.status_code != 503 or attempt == max_retries:
                    break

                # Wait a moment before retrying
                time.sleep(2)

            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and result['candidates']:
                    analysis = result['candidates'][0]['content']['parts'][0]['text']
                    return {
                        'success': True,
                        'analysis': analysis
                    }
                else:
                    return {'success': False, 'error': 'No analysis generated'}
            else:
                # Better error messages for common API issues
                if response.status_code == 503:
                    return {'success': False, 'error': 'Gemini API temporarily unavailable (503). Please try again in a few moments.'}
                elif response.status_code == 429:
                    return {'success': False, 'error': 'Gemini API rate limit exceeded (429). Please wait a moment and try again.'}
                elif response.status_code == 401:
                    return {'success': False, 'error': 'Gemini API authentication failed (401). Please check your API key.'}
                else:
                    return {'success': False, 'error': f'Gemini API error: {response.status_code}'}

        except Exception as e:
            return {'success': False, 'error': f'Analysis error: {str(e)}'}

    def generate_pdf_report(self, address: str, analysis: str, image_paths: Dict[str, str], additional_data: Dict = None) -> str:
        """Generate a professional PDF report with CV analysis"""
        if not PDF_AVAILABLE:
            return "PDF generation error: ReportLab not installed. Run: pip install reportlab"

        try:
            # Create temp directory if it doesn't exist
            temp_dir = 'temp'
            os.makedirs(temp_dir, exist_ok=True)

            # Create PDF file path with clean filename
            clean_address = address.replace(' ', '_').replace(',', '').replace('/', '_').replace('\\', '_')
            pdf_filename = f"CV_Solar_Analysis_{clean_address}.pdf"
            pdf_path = os.path.join(temp_dir, pdf_filename)

            # Remove existing file if it exists
            if os.path.exists(pdf_path):
                os.remove(pdf_path)

            print(f"📄 Creating CV PDF at: {pdf_path}")

            # Create PDF document
            doc = SimpleDocTemplate(pdf_path, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []

            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                textColor=colors.darkblue,
                alignment=1  # Center alignment
            )
            story.append(Paragraph("🌞 Professional Solar Roof Analysis Report (CV-Enhanced)", title_style))
            story.append(Spacer(1, 20))

            # Address
            address_style = ParagraphStyle(
                'Address',
                parent=styles['Heading2'],
                fontSize=16,
                spaceAfter=20,
                textColor=colors.darkgreen,
                alignment=1
            )
            story.append(Paragraph(f"📍 Property Address: {address}", address_style))
            story.append(Spacer(1, 20))

            # CV Technology note
            cv_note = """
            <b>🤖 Computer Vision Technology:</b> This analysis uses advanced computer vision algorithms
            for automated roof detection, solar panel placement optimization, and efficiency scoring.
            """
            story.append(Paragraph(cv_note, styles['Normal']))
            story.append(Spacer(1, 20))

            # Add original satellite image if available
            if 'satellite_original' in image_paths:
                story.append(Paragraph("🛰️ Original Satellite Image", styles['Heading2']))
                story.append(Spacer(1, 10))

                try:
                    img_original = RLImage(image_paths['satellite_original'], width=6*inch, height=6*inch)
                    story.append(img_original)
                    story.append(Spacer(1, 15))
                except Exception as e:
                    story.append(Paragraph(f"Error loading original satellite image: {str(e)}", styles['Normal']))
                    story.append(Spacer(1, 10))

            # Add CV overlay image if available
            if 'cv_overlay' in image_paths:
                story.append(Paragraph("🤖 Computer Vision Solar Panel Analysis", styles['Heading2']))
                story.append(Spacer(1, 10))

                try:
                    img_cv = RLImage(image_paths['cv_overlay'], width=6*inch, height=6*inch)
                    story.append(img_cv)
                    story.append(Spacer(1, 10))

                    # CV Legend
                    cv_legend_text = """
                    <b>🟢 GREEN AREAS:</b> CV-detected optimal solar installation zones<br/>
                    <b>🔴 RED AREAS:</b> CV-identified unsuitable areas (obstacles, poor orientation)<br/>
                    <b>🟡 YELLOW OUTLINE:</b> CV-detected main house boundary<br/>
                    <b>🔵 BLUE PANELS:</b> CV-optimized solar panel placement
                    """
                    story.append(Paragraph(cv_legend_text, styles['Normal']))
                    story.append(Spacer(1, 20))
                except Exception as e:
                    story.append(Paragraph(f"Error loading CV overlay image: {str(e)}", styles['Normal']))
                    story.append(Spacer(1, 10))

            # Add highlighted image if available
            if 'satellite_highlighted' in image_paths:
                story.append(Paragraph("🌞 Professional Zone Highlighting", styles['Heading2']))
                story.append(Spacer(1, 10))

                try:
                    img_highlighted = RLImage(image_paths['satellite_highlighted'], width=6*inch, height=6*inch)
                    story.append(img_highlighted)
                    story.append(Spacer(1, 20))
                except Exception as e:
                    story.append(Paragraph(f"Error loading highlighted image: {str(e)}", styles['Normal']))
                    story.append(Spacer(1, 10))

            # Add street views if available
            street_views = ['front', 'right', 'back', 'left']
            available_views = [view for view in street_views if f'street_{view}' in image_paths]

            if available_views:
                story.append(Paragraph("🏠 Multi-Angle Street Views", styles['Heading2']))
                story.append(Spacer(1, 10))

                # Create table for street views (2x2 grid)
                street_data = []
                view_labels = {'front': '🔼 Front View', 'right': '▶️ Right View', 'back': '🔽 Back View', 'left': '◀️ Left View'}

                for i in range(0, len(available_views), 2):
                    row = []
                    for j in range(2):
                        if i + j < len(available_views):
                            view = available_views[i + j]
                            try:
                                img = RLImage(image_paths[f'street_{view}'], width=2.5*inch, height=2.5*inch)
                                # Add label below image
                                cell_content = [img, Paragraph(view_labels.get(view, view.title()), styles['Normal'])]
                                row.append(cell_content)
                            except Exception as e:
                                row.append(Paragraph(f"Error loading {view} view: {str(e)}", styles['Normal']))
                        else:
                            row.append("")
                    street_data.append(row)

                street_table = Table(street_data, colWidths=[3*inch, 3*inch])
                street_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 0), (-1, -1), 10)
                ]))
                story.append(street_table)
                story.append(Spacer(1, 20))

            # Add CV technical data
            if additional_data and 'cv_data' in additional_data:
                story.append(Paragraph("🤖 Computer Vision Analysis Data", styles['Heading2']))
                story.append(Spacer(1, 10))

                cv_data = additional_data['cv_data']
                if 'summary' in cv_data:
                    summary = cv_data['summary']
                    cv_tech_data = [
                        ["🔢 Detected Panels:", f"{summary.get('panel_count', 0)}"],
                        ["📐 Usable Area:", f"{summary.get('usable_area_m2', 0)} m²"],
                        ["⚡ Est. Annual kWh:", f"{summary.get('estimated_kwh_per_year', 0)}"],
                        ["📊 Avg Efficiency Score:", f"{summary.get('average_efficiency_score', 0)}%"]
                    ]

                    cv_table = Table(cv_tech_data, colWidths=[2*inch, 4*inch])
                    cv_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
                        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 0), (-1, -1), 12),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(cv_table)
                    story.append(Spacer(1, 20))

            # Add other technical data
            if additional_data:
                story.append(Paragraph("📊 Additional Technical Data", styles['Heading2']))
                story.append(Spacer(1, 10))

                tech_data = []
                for key, value in additional_data.items():
                    if key == 'elevation':
                        tech_data.append([f"🏔️ Elevation:", f"{value:.1f} meters"])

                if tech_data:
                    tech_table = Table(tech_data, colWidths=[2*inch, 4*inch])
                    tech_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 0), (-1, -1), 12),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(tech_table)
                    story.append(Spacer(1, 20))

            # Add analysis text
            story.append(Paragraph("📋 Detailed CV-Enhanced Analysis Report", styles['Heading2']))
            story.append(Spacer(1, 10))

            # Split analysis into paragraphs and format
            analysis_paragraphs = analysis.split('\n\n')
            for para in analysis_paragraphs:
                if para.strip():
                    # Format headers
                    if para.startswith('##'):
                        header_text = para.replace('##', '').strip()
                        story.append(Paragraph(header_text, styles['Heading3']))
                    else:
                        story.append(Paragraph(para.strip(), styles['Normal']))
                    story.append(Spacer(1, 10))

            # Footer
            footer_text = f"""
            <br/><br/>
            <i>Report generated on {time.strftime('%Y-%m-%d %H:%M:%S')}</i><br/>
            <i>Powered by Enhanced Solar Analyzer with Computer Vision & Gemini 1.5 Flash AI</i>
            """
            story.append(Paragraph(footer_text, styles['Normal']))

            # Build PDF
            doc.build(story)

            # Verify PDF was created successfully
            if os.path.exists(pdf_path) and os.path.getsize(pdf_path) > 0:
                print(f"✅ CV PDF generated successfully: {pdf_path} ({os.path.getsize(pdf_path)} bytes)")
                return pdf_path
            else:
                return "PDF generation error: File was not created or is empty"

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"❌ CV PDF generation error: {str(e)}")
            print(f"Full error details: {error_details}")
            return f"PDF generation error: {str(e)}"

def main():
    """Main Streamlit application with Computer Vision integration"""

    # Initialize session state for preserving analysis results
    if 'analysis_complete' not in st.session_state:
        st.session_state.analysis_complete = False
    if 'analysis_data' not in st.session_state:
        st.session_state.analysis_data = {}

    # Custom CSS for better styling
    st.markdown("""
    <style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin: 1rem 0;
    }
    .cv-highlight {
        background-color: #e3f2fd;
        border: 2px solid #2196f3;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)

    # Header
    st.markdown('<h1 class="main-header">🌞 ROOFSNAP - Enhanced Solar Analyzer with Computer Vision</h1>', unsafe_allow_html=True)
    st.markdown("### Professional solar analysis with CV-based roof detection and PDF reports")

    # CV Technology highlight
    st.markdown("""
    <div class="cv-highlight">
        <h4>🤖 Computer Vision Technology</h4>
        <p>This application uses advanced computer vision algorithms to automatically detect roof boundaries,
        optimize solar panel placement, and generate efficiency heatmaps - no manual Solar API required!</p>
    </div>
    """, unsafe_allow_html=True)

    # Sidebar
    with st.sidebar:
        st.header("🔧 CV Analysis Settings")

        st.markdown("---")

        # Settings
        st.markdown("#### ⚙️ Analysis Settings")

        # Fixed zoom level for roof-centered analysis
        zoom_level = 20.0
        st.info("🔍 Zoom Level: Fixed at 20 for optimal roof detail")

        # Analysis options
        st.markdown("#### 🤖 CV Analysis Options")
        enable_cv_overlay = st.checkbox("Enable CV Panel Overlay", value=True, help="Show computer vision detected solar panels")
        enable_professional_highlighting = st.checkbox("Enable Professional Zone Highlighting", value=True, help="Show green/red zone highlighting")
        enable_gemini_analysis = st.checkbox("Enable Gemini AI Analysis", value=True, help="Get detailed AI analysis of the roof")
        enable_pdf_generation = st.checkbox("Generate PDF Report", value=True, help="Create professional PDF report")

        st.markdown("---")

        # API Status
        st.markdown("#### 🔑 API Status")
        if GOOGLE_MAPS_API_KEY:
            st.success("✅ Google Maps API")
        else:
            st.error("❌ Google Maps API")

        if GEMINI_API_KEY:
            st.success("✅ Gemini API")
        else:
            st.error("❌ Gemini API")

        if PDF_AVAILABLE:
            st.success("✅ PDF Generation")
        else:
            st.error("❌ PDF Generation")

        st.markdown("---")

        # CV Technology info
        st.markdown("#### 🤖 CV Technology")
        st.info("""
        **Computer Vision Features:**
        - Automated roof detection
        - Smart panel placement
        - Efficiency heatmaps
        - Obstacle identification
        - No Solar API dependency
        """)

    # Main content
    st.markdown("## 📍 Enter Property Address")

    # Address input
    col1, col2 = st.columns([3, 1])
    with col1:
        address = st.text_input(
            "Property Address",
            placeholder="Enter full address (e.g., 123 Main St, City, State, ZIP)",
            help="Enter the complete address for accurate geocoding"
        )
    with col2:
        analyze_button = st.button("🔍 Analyze with CV", type="primary", use_container_width=True)

    # Initialize analyzer
    analyzer = EnhancedSolarAnalyzerCV()

    if analyze_button and address:
        if not GOOGLE_MAPS_API_KEY:
            st.error("❌ Google Maps API key is required. Please check your .env file.")
            st.stop()

        # Clear previous results
        st.session_state.analysis_complete = False
        st.session_state.analysis_data = {}

        with st.spinner("🔍 Analyzing property with Computer Vision..."):
            # Step 1: Geocoding
            st.info("📍 Step 1: Geocoding address...")
            geocode_result = analyzer.geocode_address(address)

            if not geocode_result['success']:
                st.error(f"❌ Geocoding failed: {geocode_result['error']}")
                st.stop()

            lat = geocode_result['lat']
            lng = geocode_result['lng']
            formatted_address = geocode_result['formatted_address']

            st.success(f"✅ Address found: {formatted_address}")
            st.info(f"📍 Coordinates: {lat:.6f}, {lng:.6f}")

            # Save to CSV
            analyzer.save_to_csv(formatted_address, lat, lng, 'In Progress')

            # Step 2: Get satellite image
            st.info("🛰️ Step 2: Fetching high-resolution satellite image...")
            satellite_result = analyzer.get_satellite_image(lat, lng, zoom_level)

            if not satellite_result['success']:
                st.error(f"❌ Satellite image failed: {satellite_result['error']}")
                st.stop()

            satellite_path = satellite_result['image_path']
            st.success("✅ Satellite image obtained")

            # Step 3: Enhanced CV Analysis with Building Targeting
            st.info("🤖 Step 3: Running Enhanced Computer Vision analysis...")
            st.info(f"🎯 Targeting building at coordinates: {lat:.6f}, {lng:.6f}")
            cv_result = analyzer.get_cv_solar_analysis(satellite_path, lat, lng, int(zoom_level))

            if not cv_result['success']:
                st.error(f"❌ CV analysis failed: {cv_result['error']}")
                st.stop()

            cv_data = cv_result['cv_data']
            st.success("✅ Computer Vision analysis completed")

            # Step 4: Create overlays
            image_paths = {'satellite_original': satellite_path}

            if enable_cv_overlay:
                st.info("🎨 Step 4a: Creating CV solar panel overlay...")
                cv_overlay_path = analyzer.create_cv_solar_overlay(satellite_path, cv_data)
                image_paths['cv_overlay'] = cv_overlay_path
                st.success("✅ CV overlay created")

            if enable_professional_highlighting:
                st.info("🎨 Step 4b: Creating professional zone highlighting...")
                highlighted_path = analyzer.create_professional_highlighted_roof(satellite_path)
                image_paths['satellite_highlighted'] = highlighted_path
                st.success("✅ Professional highlighting created")

            # Step 5: Get street views
            st.info("🏠 Step 5: Fetching street view images...")
            street_views = analyzer.get_street_views_all_angles(lat, lng)

            for direction, result in street_views.items():
                if result['success']:
                    image_paths[f'street_{direction}'] = result['image_path']

            successful_views = sum(1 for result in street_views.values() if result['success'])
            st.success(f"✅ Street views obtained ({successful_views}/4 angles)")

            # Step 6: Get elevation data
            st.info("🏔️ Step 6: Getting elevation data...")
            elevation_result = analyzer.get_elevation_data(lat, lng)

            additional_data = {'cv_data': cv_data}
            if elevation_result['success']:
                additional_data['elevation'] = elevation_result['elevation']
                st.success(f"✅ Elevation: {elevation_result['elevation']:.1f} meters")
            else:
                st.warning(f"⚠️ Elevation data unavailable: {elevation_result['error']}")

            # Step 7: Gemini Analysis
            analysis_text = ""
            if enable_gemini_analysis and GEMINI_API_KEY:
                st.info("🤖 Step 7: Running Gemini AI analysis...")

                # Use the best available image for analysis
                analysis_image = image_paths.get('cv_overlay', satellite_path)
                gemini_result = analyzer.analyze_with_gemini(analysis_image, formatted_address, additional_data)

                if gemini_result['success']:
                    analysis_text = gemini_result['analysis']
                    st.success("✅ Gemini AI analysis completed")
                else:
                    st.warning(f"⚠️ Gemini analysis failed: {gemini_result['error']}")
                    analysis_text = "Gemini analysis was not available for this property."

            # Step 8: Generate PDF
            pdf_path = ""
            if enable_pdf_generation and PDF_AVAILABLE:
                st.info("📄 Step 8: Generating PDF report...")
                pdf_path = analyzer.generate_pdf_report(formatted_address, analysis_text, image_paths, additional_data)

                if pdf_path and not pdf_path.startswith("PDF generation error"):
                    st.success("✅ PDF report generated")
                else:
                    st.warning(f"⚠️ PDF generation issue: {pdf_path}")

            # Update CSV with completion
            analyzer.save_to_csv(formatted_address, lat, lng, 'Completed')

            # Store results in session state
            st.session_state.analysis_data = {
                'address': formatted_address,
                'coordinates': (lat, lng),
                'image_paths': image_paths,
                'cv_data': cv_data,
                'street_views': street_views,
                'elevation': elevation_result,
                'analysis': analysis_text,
                'pdf_path': pdf_path,
                'additional_data': additional_data
            }
            st.session_state.analysis_complete = True

        st.success("🎉 **Analysis Complete!** Results are displayed below.")

    # Display results if analysis is complete
    if st.session_state.analysis_complete and st.session_state.analysis_data:
        data = st.session_state.analysis_data

        st.markdown("---")
        st.markdown("## 📊 Analysis Results")

        # Property info
        col1, col2 = st.columns(2)
        with col1:
            st.markdown("### 📍 Property Information")
            st.write(f"**Address:** {data['address']}")
            st.write(f"**Coordinates:** {data['coordinates'][0]:.6f}, {data['coordinates'][1]:.6f}")

            if data['elevation']['success']:
                st.write(f"**Elevation:** {data['elevation']['elevation']:.1f} meters")

        with col2:
            st.markdown("### 🤖 CV Analysis Summary")
            if 'cv_data' in data and 'summary' in data['cv_data']:
                summary = data['cv_data']['summary']
                st.metric("Detected Panels", summary.get('panel_count', 0))
                st.metric("Usable Area", f"{summary.get('usable_area_m2', 0)} m²")
                st.metric("Est. Annual kWh", f"{summary.get('estimated_kwh_per_year', 0):,.0f}")
                st.metric("Avg Efficiency", f"{summary.get('average_efficiency_score', 0)}%")

        # Images section
        st.markdown("### 🖼️ Analysis Images")

        # Satellite images - Side by side comparison
        if 'satellite_original' in data['image_paths']:
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("#### 🛰️ High-resolution satellite view")
                # Use the clean original image (left side)
                if 'cv_data' in data and 'clean_image_path' in data['cv_data']:
                    st.image(data['cv_data']['clean_image_path'], caption="Original satellite image", use_column_width=True)
                else:
                    st.image(data['image_paths']['satellite_original'], caption="Original satellite image", use_column_width=True)

            with col2:
                st.markdown("#### 🤖 Computer Vision panel detection")
                # Use the enhanced overlay with yellow outline + panels (right side)
                if 'cv_overlay' in data['image_paths']:
                    st.image(data['image_paths']['cv_overlay'], caption="Building outline + solar panel analysis", use_column_width=True)
                elif 'satellite_highlighted' in data['image_paths']:
                    st.image(data['image_paths']['satellite_highlighted'], caption="Zone analysis", use_column_width=True)
                else:
                    st.warning("CV overlay not available")

        # Professional highlighting (if not shown above)
        if 'satellite_highlighted' in data['image_paths'] and 'cv_overlay' in data['image_paths']:
            st.markdown("#### 🌞 Professional Zone Highlighting")
            st.image(data['image_paths']['satellite_highlighted'], caption="Green/Red zone analysis", use_column_width=True)

        # Street views
        street_views_available = [view for view in ['front', 'right', 'back', 'left']
                                if f'street_{view}' in data['image_paths']]

        if street_views_available:
            st.markdown("#### 🏠 Street Views")

            # Display street views in a grid
            cols = st.columns(min(len(street_views_available), 4))
            view_labels = {'front': '🔼 Front', 'right': '▶️ Right', 'back': '🔽 Back', 'left': '◀️ Left'}

            for i, view in enumerate(street_views_available):
                with cols[i % 4]:
                    st.image(data['image_paths'][f'street_{view}'],
                           caption=view_labels.get(view, view.title()),
                           use_column_width=True)

        # Analysis text
        if data['analysis']:
            st.markdown("### 📋 Detailed AI Analysis")
            st.markdown(data['analysis'])

        # Downloads section
        st.markdown("### 📥 Downloads")

        col1, col2 = st.columns(2)

        with col1:
            # PDF download
            if data['pdf_path'] and not data['pdf_path'].startswith("PDF generation error"):
                with open(data['pdf_path'], 'rb') as pdf_file:
                    pdf_bytes = pdf_file.read()
                    st.download_button(
                        label="📄 Download PDF Report",
                        data=pdf_bytes,
                        file_name=os.path.basename(data['pdf_path']),
                        mime="application/pdf",
                        type="primary"
                    )
            else:
                st.info("📄 PDF report not available")

        with col2:
            # CSV download
            csv_link = analyzer.get_csv_download_link()
            if csv_link != "No search history available":
                st.markdown(csv_link, unsafe_allow_html=True)
            else:
                st.info("📊 No search history available")

        # Technical details (expandable)
        with st.expander("🔧 Technical Details"):
            st.markdown("#### 🤖 Computer Vision Data")
            if 'cv_data' in data:
                st.json(data['cv_data'])

            st.markdown("#### 🏠 Street View Results")
            for direction, result in data['street_views'].items():
                if result['success']:
                    st.success(f"✅ {direction.title()} view: {result['image_path']}")
                else:
                    st.error(f"❌ {direction.title()} view: {result['error']}")

    # Footer
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666; padding: 2rem;'>
        <p>🌞 <strong>ROOFSNAP - Enhanced Solar Analyzer with Computer Vision</strong></p>
        <p>Powered by Computer Vision, Google Maps API, and Gemini 1.5 Flash AI</p>
        <p><em>Professional solar analysis without Solar API dependency</em></p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()